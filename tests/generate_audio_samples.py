#!/usr/bin/env python3
"""Generate audio samples for testing."""

import os
import wave
import numpy as np
from pathlib import Path


def create_wav_file(filename: str, audio_data: np.ndarray, sample_rate: int = 16000):
    """Create a WAV file from audio data."""
    # Ensure audio is in the right format
    if audio_data.dtype != np.int16:
        audio_data = (audio_data * 32767).astype(np.int16)
    
    with wave.open(filename, 'wb') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_data.tobytes())


def generate_sine_wave(frequency: float, duration: float, sample_rate: int = 16000, amplitude: float = 0.5):
    """Generate a sine wave."""
    samples = int(sample_rate * duration)
    t = np.linspace(0, duration, samples, False)
    audio = amplitude * np.sin(2 * np.pi * frequency * t)
    return audio


def generate_speech_like(duration: float, sample_rate: int = 16000):
    """Generate speech-like audio with formants."""
    samples = int(sample_rate * duration)
    t = np.linspace(0, duration, samples, False)
    
    # Fundamental frequency (like vocal cords)
    f0 = 120  # Hz
    audio = 0.3 * np.sin(2 * np.pi * f0 * t)
    
    # Add harmonics
    for harmonic in range(2, 6):
        freq = f0 * harmonic
        amplitude = 0.2 / harmonic
        audio += amplitude * np.sin(2 * np.pi * freq * t)
    
    # Add formant-like resonances
    formants = [800, 1200, 2400]
    for formant in formants:
        audio += 0.1 * np.sin(2 * np.pi * formant * t) * np.exp(-t * 2)
    
    # Add some noise for realism
    noise = np.random.normal(0, 0.05, samples)
    audio += noise
    
    # Apply envelope
    envelope = np.exp(-t * 0.5) * (1 + 0.5 * np.sin(2 * np.pi * 3 * t))
    audio *= envelope
    
    # Normalize
    audio = np.clip(audio, -1, 1)
    return audio


def generate_silence(duration: float, sample_rate: int = 16000):
    """Generate silence."""
    samples = int(sample_rate * duration)
    return np.zeros(samples)


def generate_white_noise(duration: float, sample_rate: int = 16000, amplitude: float = 0.1):
    """Generate white noise."""
    samples = int(sample_rate * duration)
    return np.random.normal(0, amplitude, samples)


def main():
    """Generate all test audio samples."""
    audio_dir = Path(__file__).parent / "audio_samples"
    audio_dir.mkdir(exist_ok=True)
    
    print("Generating test audio samples...")
    
    # Generate various audio samples
    samples = {
        "speech_1s.wav": generate_speech_like(1.0),
        "speech_2s.wav": generate_speech_like(2.0),
        "speech_5s.wav": generate_speech_like(5.0),
        "silence_1s.wav": generate_silence(1.0),
        "silence_2s.wav": generate_silence(2.0),
        "noise_1s.wav": generate_white_noise(1.0),
        "tone_440hz_1s.wav": generate_sine_wave(440, 1.0),
        "tone_1000hz_1s.wav": generate_sine_wave(1000, 1.0),
        "short_speech_100ms.wav": generate_speech_like(0.1),
        "long_speech_10s.wav": generate_speech_like(10.0),
    }
    
    # Create speech with pauses
    speech_with_pauses = np.concatenate([
        generate_speech_like(1.5),
        generate_silence(0.5),
        generate_speech_like(1.0),
        generate_silence(0.3),
        generate_speech_like(2.0),
    ])
    samples["speech_with_pauses.wav"] = speech_with_pauses
    
    # Create noisy speech
    clean_speech = generate_speech_like(2.0)
    noise = generate_white_noise(2.0, amplitude=0.15)
    samples["noisy_speech.wav"] = clean_speech + noise
    
    # Generate all files
    for filename, audio_data in samples.items():
        filepath = audio_dir / filename
        create_wav_file(str(filepath), audio_data)
        print(f"Created: {filename}")
    
    print(f"Generated {len(samples)} audio samples in {audio_dir}")


if __name__ == "__main__":
    main()
