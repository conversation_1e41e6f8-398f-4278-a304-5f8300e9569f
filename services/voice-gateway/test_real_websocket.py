#!/usr/bin/env python3
"""
Real-world integration test for Voice Gateway WebSocket functionality.

This script performs comprehensive testing of the Voice Gateway WebSocket
endpoint using real JWT tokens and audio data.
"""

import asyncio
import json
import logging
import os
import sys
from pathlib import Path
from typing import Optional
import uuid

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import websockets
from websockets.exceptions import ConnectionClosed
import requests

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class RealVoiceGatewayTest:
    """Comprehensive WebSocket test using real services."""
    
    def __init__(self, 
                 voice_gateway_url: str = "ws://localhost:8002",
                 auth_service_url: str = "http://localhost:8001",
                 username: str = "testuser",
                 password: str = "testpass"):
        self.voice_gateway_url = voice_gateway_url
        self.auth_service_url = auth_service_url
        self.username = username
        self.password = password
        self.websocket = None
        self.call_id = str(uuid.uuid4())
        self.access_token = None
        
    async def authenticate(self) -> bool:
        """Authenticate with the auth service to get a real JWT token."""
        try:
            logger.info("Authenticating with auth service...")
            
            # Login to get JWT token
            login_url = f"{self.auth_service_url}/api/v1/auth/login"
            login_data = {
                "username": self.username,
                "password": self.password
            }
            
            response = requests.post(login_url, json=login_data, timeout=10)
            
            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data.get("access_token")
                logger.info("Successfully authenticated and obtained JWT token")
                return True
            else:
                logger.error(f"Authentication failed: {response.status_code} - {response.text}")
                return False
                
        except requests.RequestException as e:
            logger.error(f"Failed to connect to auth service: {e}")
            return False
    
    def create_test_token(self) -> str:
        """Create a test JWT token for development (when auth service is not available)."""
        try:
            from datetime import datetime, timedelta
            from jose import jwt
            
            # Test payload matching the expected TokenData structure
            payload = {
                "sub": str(uuid.uuid4()),  # user_id
                "username": self.username,
                "email": f"{self.username}@test.com",
                "roles": ["user"],
                "permissions": ["voice:translate"],
                "exp": datetime.utcnow() + timedelta(hours=1),
                "type": "access"
            }
            
            # Use default test secret key (should match your .env settings)
            secret_key = os.getenv("JWT_SECRET_KEY", "your-secret-key-here")
            return jwt.encode(payload, secret_key, algorithm="HS256")
            
        except ImportError:
            logger.error("jose library not available. Install with: pip install python-jose")
            return None
    
    async def check_service_health(self) -> bool:
        """Check if Voice Gateway service is running."""
        try:
            health_url = self.voice_gateway_url.replace("ws://", "http://").replace("wss://", "https://")
            response = requests.get(f"{health_url}/health", timeout=5)
            
            if response.status_code == 200:
                health_data = response.json()
                logger.info(f"Voice Gateway health: {health_data}")
                return True
            else:
                logger.error(f"Voice Gateway health check failed: {response.status_code}")
                return False
                
        except requests.RequestException as e:
            logger.error(f"Cannot reach Voice Gateway service: {e}")
            return False
    
    async def connect_websocket(self) -> bool:
        """Connect to the Voice Gateway WebSocket."""
        try:
            # Get access token
            if not self.access_token:
                if not await self.authenticate():
                    logger.info("Auth service not available, using test token...")
                    self.access_token = self.create_test_token()
                    if not self.access_token:
                        logger.error("Cannot create access token")
                        return False
            
            # Build WebSocket URL
            ws_url = f"{self.voice_gateway_url}/api/v1/call/ws/call/{self.call_id}?token={self.access_token}"
            logger.info(f"Connecting to WebSocket: {ws_url}")
            
            # Connect with timeout
            self.websocket = await asyncio.wait_for(
                websockets.connect(ws_url),
                timeout=10.0
            )
            
            logger.info("WebSocket connection established successfully")
            return True
            
        except asyncio.TimeoutError:
            logger.error("WebSocket connection timed out")
            return False
        except Exception as e:
            logger.error(f"WebSocket connection failed: {e}")
            return False
    
    async def send_control_message(self, message_type: str, **kwargs) -> bool:
        """Send a control message and wait for response."""
        try:
            message = {"type": message_type, **kwargs}
            await self.websocket.send(json.dumps(message))
            logger.info(f"Sent control message: {message_type}")
            
            # Wait for response (with timeout)
            response = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
            
            if isinstance(response, str):
                try:
                    response_data = json.loads(response)
                    logger.info(f"Received response: {response_data}")
                    return True
                except json.JSONDecodeError:
                    logger.warning(f"Received non-JSON response: {response}")
                    return False
            else:
                logger.info(f"Received binary response: {len(response)} bytes")
                return True
                
        except asyncio.TimeoutError:
            logger.error(f"Timeout waiting for response to {message_type}")
            return False
        except Exception as e:
            logger.error(f"Error sending control message {message_type}: {e}")
            return False
    
    async def test_audio_streaming(self) -> bool:
        """Test audio streaming functionality."""
        try:
            # Look for test audio files
            audio_dir = Path(__file__).parent / "tests" / "audio_samples"
            if not audio_dir.exists():
                audio_dir = Path(__file__).parent.parent.parent / "tests" / "audio_samples"
            
            test_files = []
            if audio_dir.exists():
                test_files = list(audio_dir.glob("*.wav"))
            
            if test_files:
                # Use first available audio file
                audio_file = test_files[0]
                logger.info(f"Testing with audio file: {audio_file}")
                
                # Read and send audio file
                with open(audio_file, 'rb') as f:
                    audio_data = f.read()
                    
                # Skip WAV header (44 bytes) and send raw audio
                raw_audio = audio_data[44:]
                chunk_size = 1024
                
                logger.info(f"Streaming {len(raw_audio)} bytes of audio data")
                
                # Send audio in chunks
                for i in range(0, len(raw_audio), chunk_size):
                    chunk = raw_audio[i:i + chunk_size]
                    await self.websocket.send(chunk)
                    await asyncio.sleep(0.1)  # Simulate real-time streaming
                
                logger.info("Finished streaming audio file")
                
                # Wait for translated audio response
                logger.info("Waiting for translated audio response...")
                
                timeout = 30  # Wait up to 30 seconds for translation
                start_time = asyncio.get_event_loop().time()
                
                while (asyncio.get_event_loop().time() - start_time) < timeout:
                    try:
                        response = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
                        
                        if isinstance(response, bytes):
                            logger.info(f"Received translated audio: {len(response)} bytes")
                            return True
                        else:
                            # Control message
                            try:
                                msg = json.loads(response)
                                logger.info(f"Received control message: {msg}")
                                if msg.get("type") == "error":
                                    logger.error(f"Translation error: {msg.get('message')}")
                                    return False
                            except json.JSONDecodeError:
                                pass
                                
                    except asyncio.TimeoutError:
                        continue
                
                logger.warning("No translated audio received within timeout")
                return False
                
            else:
                logger.warning("No test audio files found, skipping audio streaming test")
                return True
                
        except Exception as e:
            logger.error(f"Audio streaming test failed: {e}")
            return False
    
    async def run_comprehensive_test(self) -> bool:
        """Run a comprehensive test of all WebSocket functionality."""
        logger.info("Starting comprehensive Voice Gateway WebSocket test")
        
        # Check service health
        if not await self.check_service_health():
            logger.error("Voice Gateway service is not healthy")
            return False
        
        # Connect to WebSocket
        if not await self.connect_websocket():
            logger.error("Failed to establish WebSocket connection")
            return False
        
        try:
            # Test 1: Ping/Pong
            logger.info("Test 1: Testing ping/pong...")
            if not await self.send_control_message("ping"):
                logger.error("Ping test failed")
                return False
            
            # Test 2: Configuration update
            logger.info("Test 2: Testing configuration update...")
            if not await self.send_control_message(
                "config_update",
                config={"target_language": "es", "voice_speed": 1.0}
            ):
                logger.error("Configuration update test failed")
                return False
            
            # Test 3: Audio streaming
            logger.info("Test 3: Testing audio streaming...")
            if not await self.test_audio_streaming():
                logger.error("Audio streaming test failed")
                return False
            
            # Test 4: End call
            logger.info("Test 4: Testing call termination...")
            if not await self.send_control_message("end_call"):
                logger.error("End call test failed")
                return False
            
            logger.info("All tests passed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Test execution failed: {e}")
            return False
        finally:
            if self.websocket:
                await self.websocket.close()
                logger.info("WebSocket connection closed")


async def main():
    """Main test execution."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Real Voice Gateway WebSocket Test")
    parser.add_argument("--voice-gateway", default="ws://localhost:8002", 
                       help="Voice Gateway WebSocket URL")
    parser.add_argument("--auth-service", default="http://localhost:8001",
                       help="Auth Service URL")
    parser.add_argument("--username", default="testuser", help="Test username")
    parser.add_argument("--password", default="testpass", help="Test password")
    
    args = parser.parse_args()
    
    test = RealVoiceGatewayTest(
        voice_gateway_url=args.voice_gateway,
        auth_service_url=args.auth_service,
        username=args.username,
        password=args.password
    )
    
    success = await test.run_comprehensive_test()
    
    if success:
        logger.info("🎉 All Voice Gateway WebSocket tests passed!")
        return 0
    else:
        logger.error("❌ Voice Gateway WebSocket tests failed!")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)
