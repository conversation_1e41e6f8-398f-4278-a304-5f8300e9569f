# Service Configuration
SERVICE_NAME=voice-gateway
DEBUG=false
HOST=0.0.0.0
PORT=8002

# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USER=cortexa
DATABASE_PASSWORD=your_password_here
DATABASE_NAME=cortexa_voice
DATABASE_ECHO=false

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_GROUP_ID=voice-gateway

# Security Configuration
SECURITY_SECRET_KEY=your-super-secret-key-change-in-production
SECURITY_ALGORITHM=HS256
SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES=30
SECURITY_REFRESH_TOKEN_EXPIRE_DAYS=7

# S2ST Pipeline Configuration
WHISPER_MODEL_SIZE=base.en
WHISPER_COMPUTE_TYPE=int8
WHISPER_DEVICE=cpu

# VAD Configuration
VAD_AGGRESSIVENESS=3
VAD_FRAME_DURATION_MS=30

# Translation Configuration
TRANSLATION_MODEL=Helsinki-NLP/opus-mt-en-es
TRANSLATION_DEVICE=cpu

# TTS Configuration
TTS_PROVIDER=openai
TTS_MODEL=tts-1
TTS_VOICE=alloy
TTS_API_KEY=your_openai_api_key_here

# Audio Processing Configuration
AUDIO_SAMPLE_RATE=16000
AUDIO_CHUNK_SIZE=1024
AUDIO_CHANNELS=1

# WebSocket Configuration
WS_MAX_CONNECTIONS=100
WS_HEARTBEAT_INTERVAL=30
WS_CONNECTION_TIMEOUT=300

# External Services
CALL_DATA_SERVICE_URL=http://localhost:8003
AUTH_SERVICE_URL=http://localhost:8001

# ARQ Task Queue Configuration
ARQ_REDIS_SETTINGS=redis://localhost:6379/1
ARQ_MAX_JOBS=10
ARQ_JOB_TIMEOUT=300
