#!/usr/bin/env python3
"""
Real-world WebSocket client for testing Voice Gateway functionality.

This script connects to the Voice Gateway WebSocket endpoint and simulates
a real voice translation session with audio streaming and control messages.
"""

import asyncio
import json
import logging
import wave
from pathlib import Path
from typing import Optional
import uuid

import websockets
from websockets.exceptions import ConnectionClosed
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VoiceGatewayClient:
    """WebSocket client for testing Voice Gateway."""
    
    def __init__(self, base_url: str = "ws://localhost:8002", token: Optional[str] = None):
        self.base_url = base_url
        self.token = token or self._generate_test_token()
        self.websocket = None
        self.call_id = str(uuid.uuid4())
        
    def _generate_test_token(self) -> str:
        """Generate a test JWT token for development/testing."""
        # For testing purposes - in production, get this from auth service
        from datetime import datetime, timedelta
        from jose import jwt
        
        # Test payload
        payload = {
            "sub": str(uuid.uuid4()),
            "role": "user",
            "permissions": ["voice:translate"],
            "exp": datetime.utcnow() + timedelta(hours=1),
            "type": "access"
        }
        
        # Use a test secret key (should match your .env settings)
        secret_key = "your-test-secret-key-here"
        return jwt.encode(payload, secret_key, algorithm="HS256")
    
    async def connect(self) -> bool:
        """Connect to the Voice Gateway WebSocket."""
        try:
            url = f"{self.base_url}/api/v1/call/ws/call/{self.call_id}?token={self.token}"
            logger.info(f"Connecting to: {url}")
            
            self.websocket = await websockets.connect(url)
            logger.info("WebSocket connection established")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from the WebSocket."""
        if self.websocket:
            await self.websocket.close()
            logger.info("WebSocket connection closed")
    
    async def send_control_message(self, message_type: str, **kwargs):
        """Send a control message to the server."""
        message = {"type": message_type, **kwargs}
        await self.websocket.send(json.dumps(message))
        logger.info(f"Sent control message: {message_type}")
    
    async def send_audio_file(self, audio_file_path: str):
        """Send audio file in chunks to simulate real-time streaming."""
        try:
            with wave.open(audio_file_path, 'rb') as wav_file:
                # Skip WAV header and get raw audio data
                wav_file.readframes(wav_file.getnframes())
                wav_file.rewind()
                
                # Read header info
                sample_rate = wav_file.getframerate()
                chunk_size = 1024  # bytes per chunk
                
                logger.info(f"Streaming audio file: {audio_file_path}")
                logger.info(f"Sample rate: {sample_rate}, Chunk size: {chunk_size}")
                
                # Stream audio in chunks
                while True:
                    chunk = wav_file.readframes(chunk_size // 2)  # 2 bytes per sample
                    if not chunk:
                        break
                    
                    await self.websocket.send(chunk)
                    logger.debug(f"Sent audio chunk: {len(chunk)} bytes")
                    
                    # Simulate real-time streaming delay
                    await asyncio.sleep(0.1)
                
                logger.info("Finished streaming audio file")
                
        except Exception as e:
            logger.error(f"Error sending audio file: {e}")
    
    async def send_synthetic_audio(self, duration: float = 3.0):
        """Send synthetic speech-like audio for testing."""
        sample_rate = 16000
        samples = int(sample_rate * duration)
        chunk_size = 1024
        
        logger.info(f"Generating and streaming {duration}s of synthetic audio")
        
        # Generate speech-like audio
        t = np.linspace(0, duration, samples, False)
        
        # Fundamental frequency (like vocal cords)
        f0 = 120  # Hz
        audio = 0.3 * np.sin(2 * np.pi * f0 * t)
        
        # Add harmonics
        for harmonic in range(2, 6):
            freq = f0 * harmonic
            amplitude = 0.2 / harmonic
            audio += amplitude * np.sin(2 * np.pi * freq * t)
        
        # Convert to 16-bit PCM
        audio_int16 = (audio * 32767).astype(np.int16)
        
        # Send in chunks
        for i in range(0, len(audio_int16), chunk_size // 2):
            chunk = audio_int16[i:i + chunk_size // 2]
            chunk_bytes = chunk.tobytes()
            
            await self.websocket.send(chunk_bytes)
            logger.debug(f"Sent synthetic audio chunk: {len(chunk_bytes)} bytes")
            
            # Simulate real-time streaming
            await asyncio.sleep(0.1)
        
        logger.info("Finished streaming synthetic audio")
    
    async def listen_for_responses(self):
        """Listen for responses from the server."""
        try:
            async for message in self.websocket:
                if isinstance(message, bytes):
                    # Received audio data
                    logger.info(f"Received translated audio: {len(message)} bytes")
                    # In a real client, you would play this audio
                    
                else:
                    # Received control message
                    try:
                        data = json.loads(message)
                        logger.info(f"Received control message: {data}")
                        
                        if data.get("type") == "connection_established":
                            logger.info("Connection confirmed by server")
                        elif data.get("type") == "pong":
                            logger.info("Received pong response")
                        elif data.get("type") == "error":
                            logger.error(f"Server error: {data.get('message')}")
                            
                    except json.JSONDecodeError:
                        logger.warning(f"Received non-JSON text message: {message}")
                        
        except ConnectionClosed:
            logger.info("Server closed the connection")
        except Exception as e:
            logger.error(f"Error listening for responses: {e}")
    
    async def run_test_session(self, audio_file: Optional[str] = None):
        """Run a complete test session."""
        if not await self.connect():
            return
        
        try:
            # Start listening for responses in background
            listen_task = asyncio.create_task(self.listen_for_responses())
            
            # Wait a moment for connection to stabilize
            await asyncio.sleep(1)
            
            # Send ping to test control messages
            await self.send_control_message("ping")
            await asyncio.sleep(1)
            
            # Update configuration
            await self.send_control_message(
                "config_update",
                config={
                    "target_language": "es",
                    "voice_speed": 1.0
                }
            )
            await asyncio.sleep(1)
            
            # Send audio
            if audio_file and Path(audio_file).exists():
                await self.send_audio_file(audio_file)
            else:
                logger.info("No audio file provided, sending synthetic audio")
                await self.send_synthetic_audio(duration=3.0)
            
            # Wait for processing
            await asyncio.sleep(5)
            
            # End the call
            await self.send_control_message("end_call")
            await asyncio.sleep(2)
            
            # Cancel listening task
            listen_task.cancel()
            
        except Exception as e:
            logger.error(f"Error during test session: {e}")
        finally:
            await self.disconnect()


async def main():
    """Main test function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Voice Gateway WebSocket")
    parser.add_argument("--url", default="ws://localhost:8002", help="WebSocket base URL")
    parser.add_argument("--token", help="JWT token (will generate test token if not provided)")
    parser.add_argument("--audio-file", help="Path to audio file to stream")
    parser.add_argument("--duration", type=float, default=3.0, help="Duration of synthetic audio")
    
    args = parser.parse_args()
    
    client = VoiceGatewayClient(base_url=args.url, token=args.token)
    await client.run_test_session(audio_file=args.audio_file)


if __name__ == "__main__":
    # Install required packages: pip install websockets jose numpy
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        exit(1)
