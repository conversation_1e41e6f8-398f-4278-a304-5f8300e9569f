#!/bin/bash

# Voice Gateway WebSocket Testing Setup Script
# This script helps set up everything needed to test the Voice Gateway WebSocket functionality

set -e

echo "🚀 Setting up Voice Gateway WebSocket Testing Environment"
echo "========================================================"

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    echo "❌ Error: Please run this script from the voice-gateway service directory"
    exit 1
fi

# Install required Python packages for testing
echo "📦 Installing required Python packages..."
poetry add --group dev websockets python-jose[cryptography] numpy requests

# Check if test audio samples exist
echo "🎵 Checking for test audio samples..."
AUDIO_DIR="../../tests/audio_samples"
if [ ! -d "$AUDIO_DIR" ]; then
    echo "📁 Creating test audio samples directory..."
    mkdir -p "$AUDIO_DIR"
    
    # Generate test audio samples if the script exists
    if [ -f "../../tests/generate_audio_samples.py" ]; then
        echo "🎼 Generating test audio samples..."
        cd ../../tests
        python generate_audio_samples.py
        cd - > /dev/null
    else
        echo "⚠️  Test audio generation script not found. You can manually add audio files to $AUDIO_DIR"
    fi
else
    echo "✅ Test audio samples directory exists"
fi

# Check if .env file exists
echo "⚙️  Checking environment configuration..."
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your actual configuration values"
else
    echo "✅ .env file exists"
fi

# Make test scripts executable
echo "🔧 Making test scripts executable..."
chmod +x test_websocket_client.py
chmod +x test_real_websocket.py

echo ""
echo "✅ Setup complete! Here's how to test the Voice Gateway WebSocket:"
echo ""
echo "1. 🌐 Browser Testing (easiest):"
echo "   - Open test_websocket_client.html in your browser"
echo "   - Make sure Voice Gateway is running on localhost:8002"
echo "   - Click 'Connect' and test various features"
echo ""
echo "2. 🐍 Python Client Testing:"
echo "   - Basic test: python test_websocket_client.py"
echo "   - With audio file: python test_websocket_client.py --audio-file path/to/audio.wav"
echo "   - Custom URL: python test_websocket_client.py --url ws://your-server:8002"
echo ""
echo "3. 🧪 Comprehensive Integration Test:"
echo "   - Full test: python test_real_websocket.py"
echo "   - Custom services: python test_real_websocket.py --voice-gateway ws://localhost:8002 --auth-service http://localhost:8001"
echo ""
echo "4. 📋 Prerequisites:"
echo "   - Voice Gateway service running (poetry run uvicorn src.main:app --host 0.0.0.0 --port 8002)"
echo "   - Auth service running (for real JWT tokens)"
echo "   - Redis running (for ARQ task queue)"
echo "   - External services configured (OpenAI API key for TTS, etc.)"
echo ""
echo "5. 🔍 Debugging Tips:"
echo "   - Check service logs for detailed error messages"
echo "   - Use browser dev tools to inspect WebSocket messages"
echo "   - Verify JWT token is valid and has correct permissions"
echo "   - Ensure audio files are in correct format (16kHz, mono, WAV)"
echo ""
echo "Happy testing! 🎉"
