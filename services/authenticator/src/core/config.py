from pydantic import Field

from cortexacommon.config import BaseServiceSettings


class AuthServiceSettings(BaseServiceSettings):
    """Settings for the authentication service."""
    
    service_name: str = Field(
        default="cortexa-auth-service",
        description="Name of the authentication service"
    )
    
    port: int = Field(
        default=8001,
        description="Port for the authentication service"
    )
    
    # Additional auth-specific settings can be added here
    # For example:
    # max_login_attempts: int = Field(default=5, description="Maximum login attempts")
    # account_lockout_duration: int = Field(default=300, description="Account lockout duration in seconds")


# Global settings instance
settings = AuthServiceSettings()
