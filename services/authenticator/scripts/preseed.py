#!/usr/bin/env python3
"""Preseed script to create initial admin user and test data."""

import asyncio
import sys
from pathlib import Path

# Add the src directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from sqlalchemy.ext.asyncio import AsyncSession
from cortexacommon.db import init_db, get_async_session
from src.core.config import settings
from src.core.roles import UserRole
from src.crud.crud_user import UserRepository
from src.schemas.user import UserCreate


async def create_admin_user(
    username: str = "admin",
    email: str = "<EMAIL>",
    password: str = "AdminPassword123!",
) -> None:
    """Create an admin user if it doesn't exist."""
    print(f"🔧 Creating admin user: {username}")
    
    async for session in get_async_session():
        user_repo = UserRepository(session)
        
        # Check if admin user already exists
        existing_user = await user_repo.get_user_by_username(username)
        if existing_user:
            print(f"✅ Admin user '{username}' already exists")
            return
        
        # Check if email is already taken
        existing_email = await user_repo.get_user_by_email(email)
        if existing_email:
            print(f"❌ Email '{email}' is already taken by user '{existing_email.username}'")
            return
        
        # Create admin user
        admin_data = UserCreate(
            username=username,
            email=email,
            password=password,
            role=UserRole.ADMIN.value,
            is_active=True
        )
        
        try:
            admin_user = await user_repo.create_user(admin_data)
            print(f"✅ Admin user created successfully!")
            print(f"   Username: {admin_user.username}")
            print(f"   Email: {admin_user.email}")
            print(f"   Role: {admin_user.role}")
            print(f"   ID: {admin_user.id}")
        except Exception as e:
            print(f"❌ Failed to create admin user: {e}")
            raise


async def create_test_users() -> None:
    """Create additional test users for different roles."""
    test_users = [
        {
            "username": "manager",
            "email": "<EMAIL>",
            "password": "ManagerPassword123!",
            "role": UserRole.MANAGER.value,
        },
        {
            "username": "operator",
            "email": "<EMAIL>",
            "password": "OperatorPassword123!",
            "role": UserRole.OPERATOR.value,
        },
        {
            "username": "translator",
            "email": "<EMAIL>",
            "password": "TranslatorPassword123!",
            "role": UserRole.TRANSLATOR.value,
        },
    ]
    
    print(f"\n🔧 Creating test users...")
    
    async for session in get_async_session():
        user_repo = UserRepository(session)
        
        for user_data in test_users:
            # Check if user already exists
            existing_user = await user_repo.get_user_by_username(user_data["username"])
            if existing_user:
                print(f"✅ User '{user_data['username']}' already exists")
                continue
            
            # Check if email is already taken
            existing_email = await user_repo.get_user_by_email(user_data["email"])
            if existing_email:
                print(f"❌ Email '{user_data['email']}' is already taken")
                continue
            
            try:
                user_create = UserCreate(**user_data, is_active=True)
                user = await user_repo.create_user(user_create)
                print(f"✅ Created {user.role.lower()} user: {user.username}")
            except Exception as e:
                print(f"❌ Failed to create user '{user_data['username']}': {e}")


async def main():
    """Main preseed function."""
    print("🚀 Starting Cortexa Auth Service Preseed")
    print(f"📊 Database URL: {settings.database.url}")
    
    # Initialize database
    try:
        init_db(settings.database)
        print("✅ Database initialized")
    except Exception as e:
        print(f"❌ Failed to initialize database: {e}")
        sys.exit(1)
    
    try:
        # Create admin user
        await create_admin_user()
        
        # Create test users
        await create_test_users()
        
        print("\n🎉 Preseed completed successfully!")
        print("\n📋 Available users:")
        print("   <EMAIL> (ADMIN) - AdminPassword123!")
        print("   <EMAIL> (MANAGER) - ManagerPassword123!")
        print("   <EMAIL> (OPERATOR) - OperatorPassword123!")
        print("   <EMAIL> (TRANSLATOR) - TranslatorPassword123!")
        
    except Exception as e:
        print(f"❌ Preseed failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
