# Auth Service Management Scripts

This directory contains management scripts for the Cortexa Authentication Service.

## Quick Start

The easiest way to get started is to use the management script:

```bash
# Run database migrations
./scripts/manage.sh migrate

# Create default admin and test users
./scripts/manage.sh preseed

# Start the service
./scripts/manage.sh start
```

## Available Scripts

### `manage.sh` - Main Management Script

A convenient wrapper script that provides common operations:

```bash
# Create default users (admin, manager, operator, translator)
./scripts/manage.sh preseed

# Create just the admin user
./scripts/manage.sh create-admin

# Create a custom user
./scripts/manage.sh create-<NAME_EMAIL> MyPassword123! --role MANAGER

# List all users
./scripts/manage.sh list-users

# Run database migrations
./scripts/manage.sh migrate

# Run tests
./scripts/manage.sh test

# Start the service
./scripts/manage.sh start
```

### `preseed.py` - Database Seeding

Creates initial users for testing and development:

```bash
poetry run python scripts/preseed.py
```

Creates:
- **admin** (ADMIN) - `<EMAIL>` / `AdminPassword123!`
- **manager** (MANAGER) - `<EMAIL>` / `ManagerPassword123!`
- **operator** (OPERATOR) - `<EMAIL>` / `OperatorPassword123!`
- **translator** (TRANSLATOR) - `<EMAIL>` / `TranslatorPassword123!`

### `create_user.py` - User Management CLI

More flexible user creation and management:

```bash
# Create a user
poetry run python scripts/create_user.py <NAME_EMAIL> password123 --role ADMIN

# List all users
poetry run python scripts/create_user.py list

# Run preseed
poetry run python scripts/create_user.py preseed
```

## Default Test Users

After running preseed, you'll have these users available for testing:

| Username | Email | Password | Role |
|----------|-------|----------|------|
| admin | <EMAIL> | AdminPassword123! | ADMIN |
| manager | <EMAIL> | ManagerPassword123! | MANAGER |
| operator | <EMAIL> | OperatorPassword123! | OPERATOR |
| translator | <EMAIL> | TranslatorPassword123! | TRANSLATOR |

## Testing the API

Once you have users created, you can test the API:

```bash
# Login to get a token
curl -X POST "http://localhost:8001/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "AdminPassword123!"}'

# Use the token to access protected endpoints
curl -X GET "http://localhost:8001/api/v1/users/me" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## Environment Setup

Make sure you have:

1. **Database running** (PostgreSQL)
2. **Environment variables set** (see `.env` file)
3. **Dependencies installed**: `poetry install`
4. **Migrations run**: `./scripts/manage.sh migrate`

## Troubleshooting

### Database Connection Issues

If you get database connection errors:

1. Check that PostgreSQL is running
2. Verify database credentials in `.env`
3. Ensure the database exists: `createdb cortexa_auth`

### Permission Errors

If you get permission errors running scripts:

```bash
chmod +x scripts/manage.sh
```

### Import Errors

If you get import errors, make sure you're running from the project root and have installed dependencies:

```bash
cd services/cortexa-auth-service
poetry install
```
