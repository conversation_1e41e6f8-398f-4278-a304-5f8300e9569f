#!/bin/bash
# Management script for Cortexa Auth Service

set -e

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Change to project directory
cd "$PROJECT_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_usage() {
    echo -e "${BLUE}Cortexa Auth Service Management Script${NC}"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  preseed                    - Create default admin and test users"
    echo "  create-admin               - Create admin user only"
    echo "  create-user <args>         - Create a custom user"
    echo "  list-users                 - List all users"
    echo "  migrate                    - Run database migrations"
    echo "  test                       - Run tests"
    echo "  start                      - Start the service"
    echo ""
    echo "Examples:"
    echo "  $0 preseed"
    echo "  $0 create-<NAME_EMAIL> MyPassword123! --role MANAGER"
    echo "  $0 list-users"
    echo ""
}

run_preseed() {
    echo -e "${GREEN}🚀 Running preseed...${NC}"
    poetry run python scripts/preseed.py
}

create_admin() {
    echo -e "${GREEN}🔧 Creating admin user...${NC}"
    poetry run python scripts/create_user.py <NAME_EMAIL> AdminPassword123! --role ADMIN
}

create_user() {
    echo -e "${GREEN}🔧 Creating user...${NC}"
    poetry run python scripts/create_user.py create "$@"
}

list_users() {
    echo -e "${GREEN}👥 Listing users...${NC}"
    poetry run python scripts/create_user.py list
}

run_migrations() {
    echo -e "${GREEN}🗄️  Running database migrations...${NC}"
    poetry run alembic upgrade head
}

run_tests() {
    echo -e "${GREEN}🧪 Running tests...${NC}"
    poetry run pytest tests/ -v
}

start_service() {
    echo -e "${GREEN}🚀 Starting auth service...${NC}"
    poetry run uvicorn src.main:app --host 0.0.0.0 --port 8001 --reload
}

# Check if poetry is available
if ! command -v poetry &> /dev/null; then
    echo -e "${RED}❌ Poetry is not installed or not in PATH${NC}"
    echo "Please install Poetry: https://python-poetry.org/docs/#installation"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    echo -e "${RED}❌ Not in the auth service directory${NC}"
    echo "Please run this script from the cortexa-auth-service directory"
    exit 1
fi

# Parse command
case "${1:-}" in
    "preseed")
        run_preseed
        ;;
    "create-admin")
        create_admin
        ;;
    "create-user")
        shift
        if [ $# -lt 3 ]; then
            echo -e "${RED}❌ create-user requires at least 3 arguments: username email password${NC}"
            echo "Usage: $0 create-user <username> <email> <password> [--role ROLE] [--inactive]"
            exit 1
        fi
        create_user "$@"
        ;;
    "list-users")
        list_users
        ;;
    "migrate")
        run_migrations
        ;;
    "test")
        run_tests
        ;;
    "start")
        start_service
        ;;
    "help"|"-h"|"--help")
        print_usage
        ;;
    "")
        print_usage
        ;;
    *)
        echo -e "${RED}❌ Unknown command: $1${NC}"
        echo ""
        print_usage
        exit 1
        ;;
esac
