#!/usr/bin/env python3
"""CLI script to create users in the auth service."""

import asyncio
import sys
import argparse
from pathlib import Path

# Add the src directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from cortexacommon.db import init_db, get_async_session
from src.core.config import settings
from src.core.roles import UserRole, get_valid_roles
from src.crud.crud_user import UserRepository
from src.schemas.user import UserCreate


async def create_user(
    username: str,
    email: str,
    password: str,
    role: str = UserRole.OPERATOR.value,
    active: bool = True
) -> None:
    """Create a user with the given parameters."""
    print(f"🔧 Creating user: {username}")
    
    # Validate role
    valid_roles = get_valid_roles()
    if role not in valid_roles:
        print(f"❌ Invalid role '{role}'. Valid roles: {', '.join(valid_roles)}")
        return
    
    async for session in get_async_session():
        user_repo = UserRepository(session)
        
        # Check if user already exists
        existing_user = await user_repo.get_user_by_username(username)
        if existing_user:
            print(f"❌ User '{username}' already exists")
            return
        
        # Check if email is already taken
        existing_email = await user_repo.get_user_by_email(email)
        if existing_email:
            print(f"❌ Email '{email}' is already taken by user '{existing_email.username}'")
            return
        
        # Create user
        user_data = UserCreate(
            username=username,
            email=email,
            password=password,
            role=role,
            is_active=active
        )
        
        try:
            user = await user_repo.create_user(user_data)
            print(f"✅ User created successfully!")
            print(f"   Username: {user.username}")
            print(f"   Email: {user.email}")
            print(f"   Role: {user.role}")
            print(f"   Active: {user.is_active}")
            print(f"   ID: {user.id}")
        except Exception as e:
            print(f"❌ Failed to create user: {e}")
            raise


async def list_users() -> None:
    """List all users in the system."""
    print("👥 Listing all users:")
    
    async for session in get_async_session():
        user_repo = UserRepository(session)
        
        try:
            users = await user_repo.get_multi(limit=100)
            
            if not users:
                print("   No users found")
                return
            
            print(f"   Found {len(users)} users:")
            print("   " + "-" * 80)
            print(f"   {'Username':<20} {'Email':<30} {'Role':<12} {'Active':<8} {'ID'}")
            print("   " + "-" * 80)
            
            for user in users:
                status = "✅" if user.is_active else "❌"
                print(f"   {user.username:<20} {user.email:<30} {user.role:<12} {status:<8} {user.id}")
                
        except Exception as e:
            print(f"❌ Failed to list users: {e}")


def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(description="Create and manage users in Cortexa Auth Service")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Create user command
    create_parser = subparsers.add_parser("create", help="Create a new user")
    create_parser.add_argument("username", help="Username for the new user")
    create_parser.add_argument("email", help="Email for the new user")
    create_parser.add_argument("password", help="Password for the new user")
    create_parser.add_argument("--role", default=UserRole.OPERATOR.value, 
                              choices=list(get_valid_roles()),
                              help="Role for the new user")
    create_parser.add_argument("--inactive", action="store_true", 
                              help="Create user as inactive")
    
    # List users command
    list_parser = subparsers.add_parser("list", help="List all users")
    
    # Preseed command
    preseed_parser = subparsers.add_parser("preseed", help="Create default test users")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print("🚀 Cortexa Auth Service User Management")
    print(f"📊 Database URL: {settings.database.url}")
    
    # Initialize database
    try:
        init_db(settings.database)
        print("✅ Database initialized")
    except Exception as e:
        print(f"❌ Failed to initialize database: {e}")
        sys.exit(1)
    
    async def run_command():
        if args.command == "create":
            await create_user(
                username=args.username,
                email=args.email,
                password=args.password,
                role=args.role,
                active=not args.inactive
            )
        elif args.command == "list":
            await list_users()
        elif args.command == "preseed":
            # Import and run preseed
            from preseed import create_admin_user, create_test_users
            await create_admin_user()
            await create_test_users()
            print("\n🎉 Preseed completed!")
    
    try:
        asyncio.run(run_command())
    except Exception as e:
        print(f"❌ Command failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
